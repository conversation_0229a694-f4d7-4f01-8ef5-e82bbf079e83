import 'package:flutter/material.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:provider/provider.dart';

class ExtractDetailsObjectMobile extends StatefulWidget {
  const ExtractDetailsObjectMobile({super.key});

  @override
  State<ExtractDetailsObjectMobile> createState() =>
      _ExtractDetailsObjectMobileState();
}

class _ExtractDetailsObjectMobileState
    extends State<ExtractDetailsObjectMobile> {
  @override
  Widget build(BuildContext context) {
    return Consumer<WebHomeProviderStatic>(
      builder: (context, provider, child) {
        return Scaffold(
          backgroundColor: Colors.black,
          body: Column(
            children: [
              // Header with toggle
              _buildHeader(context, provider),

              // Content area
              Expanded(
                child: Container(
                  color: Colors.white,
                  child: _buildContent(context, provider),
                ),
              ),

              // Bottom action buttons
              _buildBottomActions(context),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, WebHomeProviderStatic provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.black,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Dynamic header label based on toggle state
          Text(
            provider.isAIMode ? 'Extracted Details' : 'Objects',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.white,
              fontWeight: FontWeight.w600,
              height: 1,
            ),
          ),

          // AI/Manual Toggle
          _buildAIManualToggle(context, provider),
        ],
      ),
    );
  }

  Widget _buildAIManualToggle(
      BuildContext context, WebHomeProviderStatic provider) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          provider.isAIMode ? 'Form' : 'Manually Process',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.white,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
        const SizedBox(width: 8),
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              provider.toggleAIMode();
            },
            child: Container(
              width: 40,
              height: 20,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                  color: Colors.white,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(10),
              ),
              child: AnimatedAlign(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                alignment: provider.isAIMode
                    ? Alignment.centerLeft
                    : Alignment.centerRight,
                child: Container(
                  width: 18,
                  height: 18,
                  margin: const EdgeInsets.all(1),
                  decoration: const BoxDecoration(
                    color: Color(0xFF0058FF),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context, WebHomeProviderStatic provider) {
    if (provider.isAIMode) {
      return _buildExtractedDetailsTab(context);
    } else {
      return _buildObjectsTab(context);
    }
  }

  Widget _buildExtractedDetailsTab(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          _buildHeaderSection(context),
          const SizedBox(height: 20),

          // Stack items
          _buildStackItem(context, 'I.O Details', 'Partial Complete', '12 I.O'),
          _buildStackItem(
              context, 'Inputs Stack', 'Partial Complete', '25 Attributes'),
          _buildStackItem(
              context, 'Output Stack', 'Partial Complete', 'Configured'),
          _buildStackItem(
              context, 'Validation Stack', 'Partial Complete', '2 Rules'),
          _buildStackItem(context, 'UI Stack', 'Missing', '2 Configured'),
          _buildStackItem(context, 'Mapping Stack', 'Missing', '3 Mapping'),
          _buildStackItem(
              context, 'Nested Function Pathways', 'Missing', '1 Pathways'),

          const SizedBox(height: 20),

          // Execution Pathway section
          _buildExecutionPathway(context),
        ],
      ),
    );
  }

  Widget _buildObjectsTab(BuildContext context) {
    return const Center(
      child: Text(
        'Objects View',
        style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Manually Process',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey.shade600,
            fontWeight: FontWeight.w400,
            height: 1,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Solution: Customer Onboarding',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleMedium(context),
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
            fontWeight: FontWeight.w600,
            height: 1.2,
          ),
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Icon(Icons.check_circle_outline,
                size: 16, color: Colors.grey.shade600),
            const SizedBox(width: 4),
            Text(
              'System verifies Budget availability.',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w400,
                height: 1,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStackItem(
      BuildContext context, String title, String status, String count) {
    Color statusColor = _getStatusColor(status);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyLarge(context),
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                    height: 1.2,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  status,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: statusColor,
                    fontWeight: FontWeight.w400,
                    height: 1,
                  ),
                ),
              ],
            ),
          ),
          Text(
            count,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w500,
              height: 1,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExecutionPathway(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Icon(Icons.menu, size: 16, color: Colors.grey.shade600),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Execution Pathway',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyLarge(context),
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
                fontWeight: FontWeight.w600,
                height: 1.2,
              ),
            ),
          ),
          Text(
            '3 Routes',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w500,
              height: 1,
            ),
          ),
          const SizedBox(width: 8),
          // Colored circles
          Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: const BoxDecoration(
                  color: Colors.blue,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 4),
              Container(
                width: 12,
                height: 12,
                decoration: const BoxDecoration(
                  color: Colors.purple,
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey.shade200, width: 1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                // Edit action
              },
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                side: BorderSide(color: Colors.grey.shade400),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              child: Text(
                'Edit',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                  fontWeight: FontWeight.w500,
                  height: 1,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                // Delete action
              },
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                side: BorderSide(color: Colors.red.shade300),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.red,
                  fontWeight: FontWeight.w500,
                  height: 1,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'partial complete':
        return Colors.orange;
      case 'missing':
        return Colors.red;
      case 'complete':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}
