import 'package:flutter/material.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/screens/web/static_flow/ai_validation_popup.dart';

class AiObjectRightPanel extends StatefulWidget {
  const AiObjectRightPanel({super.key});

  @override
  State<AiObjectRightPanel> createState() => _AiObjectRightPanelState();
}

class _AiObjectRightPanelState extends State<AiObjectRightPanel> {
  int currentPage = 1;
  int itemsPerPage = 5; // Default fallback value

  // Pagination for Industry Bundles
  int currentBundlePage = 1;
  int bundleItemsPerPage = 2; // Default fallback value

  // Estimated heights for dynamic calculation (further reduced based on actual UI)
  static const double validationItemHeight =
      45.0; // Approximate height of each validation item (further reduced)
  static const double headerHeight =
      30.0; // Height of section headers (further reduced)
  static const double paginationHeight =
      25.0; // Height of pagination controls (further reduced)
  static const double padding = 10.0; // Various paddings (further reduced)

  // Sample data for validation rules
  final List<Map<String, String>> validationRules = [
    {
      'title': 'Email Validation Rules',
      'description': 'Add REGEX EMAIL and IS_UNIQUE operators for email field',
      'category': 'Business Rules',
      'complexity': 'SIMPLE',
    },
    {
      'title': 'Phone Format Validation',
      'description':
          'Apply MATCHES_PATTERN operator for international phone numbers',
      'category': 'Business Rules',
      'complexity': 'MODERATE',
    },
    {
      'title': 'Customer-Address Relationship',
      'description':
          'Configure one-to-many with CASCADE delete for address cleanup',
      'category': 'Entity Relationship',
      'complexity': 'MODERATE',
    },
    {
      'title': 'Title of the Issue',
      'description': 'Description of the Issue',
      'category': 'Business Rules',
      'complexity': 'MODERATE',
    },
    {
      'title': 'Address Auto-Complete',
      'description': 'Implement auto-complete for the field',
      'category': 'Entity Relationship',
      'complexity': 'MODERATE',
    },
    {
      'title': 'Title of the Issue',
      'description': 'Description of the Issue',
      'category': 'Business Rules',
      'complexity': 'MODERATE',
    },
    {
      'title': 'Data Validation Rules',
      'description': 'Additional validation for data integrity',
      'category': 'Business Rules',
      'complexity': 'SIMPLE',
    },
    {
      'title': 'Security Validation',
      'description': 'Implement security checks for sensitive data',
      'category': 'Security',
      'complexity': 'COMPLEX',
    },
  ];

  // Sample data for industry bundles
  final List<Map<String, String>> industryBundles = [
    {
      'title': 'E-Commerce Complete',
      'match': '92% match',
      'modules': '23 resolutions',
      'description':
          'Full e-commerce customer entity with privacy, UX, and business intelligence',
    },
    {
      'title': 'Security First',
      'match': '92% match',
      'modules': '23 resolutions',
      'description': 'Enhanced security and compliance for sensitive data',
    },
    {
      'title': 'Healthcare Bundle',
      'match': '88% match',
      'modules': '18 resolutions',
      'description':
          'HIPAA compliant healthcare data management with patient privacy controls',
    },
    {
      'title': 'Financial Services',
      'match': '95% match',
      'modules': '31 resolutions',
      'description':
          'Banking and financial compliance with fraud detection and audit trails',
    },
  ];

  int get totalPages => (validationRules.length / itemsPerPage).ceil();
  int get totalBundlePages =>
      (industryBundles.length / bundleItemsPerPage).ceil();

  List<Map<String, String>> get currentPageItems {
    final startIndex = (currentPage - 1) * itemsPerPage;
    final endIndex =
        (startIndex + itemsPerPage).clamp(0, validationRules.length);
    return validationRules.sublist(startIndex, endIndex);
  }

  List<Map<String, String>> get currentBundlePageItems {
    final startIndex = (currentBundlePage - 1) * bundleItemsPerPage;
    final endIndex =
        (startIndex + bundleItemsPerPage).clamp(0, industryBundles.length);
    return industryBundles.sublist(startIndex, endIndex);
  }

  // Calculate dynamic items per page for validation rules based on available height
  int calculateValidationItemsPerPage(double availableHeight) {
    final contentHeight =
        availableHeight - headerHeight - paginationHeight - padding;
    final maxItems = (contentHeight / validationItemHeight).floor();

    // Debug print to understand the calculation
    print(
        'DEBUG: availableHeight: $availableHeight, contentHeight: $contentHeight, maxItems: $maxItems');

    return maxItems > 0 ? maxItems : 1; // Ensure at least 1 item is shown
  }

  // Calculate items per page for industry bundles based on screen width
  int calculateBundleItemsPerPage(
      double availableHeight, BuildContext context) {
    // Get screen width to determine exact items based on screen size
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth >= 1920) {
      return 2; // Exactly 2 items for 1920px and above
    } else if (screenWidth >= 1366) {
      return 1; // Exactly 1 item for 1366px and above
    } else {
      return 1; // Default to 1 item for smaller screens
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent,
      child: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(
          scrollbars: false,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section 1: Validation Rules with Header + Body (takes full available height)
            Expanded(child: _buildValidationRulesSection()),
            const SizedBox(height: 10),
            // Section 2: Industry Bundles with Header + Body (natural size)
            _buildIndustryBundlesSection(),
          ],
        ),
      ),
    );
  }

  // Reusable header widget for both sections
  Widget _buildSectionHeader({
    required BuildContext context,
    required String title,
    IconData? icon, // Made optional
    required List<Color> gradientColors,
    Widget? actionButton,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: gradientColors),
      ),
      child: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              color: Colors.white,
              size: 16,
            ),
            const SizedBox(width: 4),
          ],
          Expanded(
            child: Text(
              title,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleSmall(context),
                fontWeight: FontManager.semiBold,
                color: Colors.white,
                fontFamily: FontManager.fontFamilyTiemposText,
                height: 1.2,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (actionButton != null) actionButton,
        ],
      ),
    );
  }

  Widget _buildValidationRulesSection() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate dynamic items per page based on available height
        final dynamicItemsPerPage =
            calculateValidationItemsPerPage(constraints.maxHeight);

        // Update itemsPerPage if it's different from current value
        if (itemsPerPage != dynamicItemsPerPage) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            setState(() {
              itemsPerPage = dynamicItemsPerPage;
              // Reset to first page if current page is now out of bounds
              if (currentPage > totalPages) {
                currentPage = 1;
              }
            });
          });
        }

        return Container(
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFD0D0D0), width: 0.5),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: [
              // Section 1 Header
              _buildSectionHeader(
                context: context,
                title: 'AI Smart Resolution (${validationRules.length})',
                icon: Icons.auto_awesome,
                gradientColors: const [Color(0xff0058FF), Color(0xff0B3A91)],
                actionButton: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'BULK APPLY',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontManager.semiBold,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      height: 1.2,
                    ),
                  ),
                ),
              ),
              // Section 1 Body (Dynamic Height)
              Expanded(
                child: Container(
                  color: Colors.white,
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      // Expanded SingleChildScrollView for rule items to prevent overflow
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(10, 10, 10, 0),
                          child: SingleChildScrollView(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children:
                                  currentPageItems.asMap().entries.map((entry) {
                                final index = entry.key;
                                final item = entry.value;
                                return Padding(
                                  padding: EdgeInsets.only(
                                    bottom: index < currentPageItems.length - 1
                                        ? 4
                                        : 0,
                                  ),
                                  child: _buildValidationRuleItem(
                                    item['title']!,
                                    item['description']!,
                                    item['category']!,
                                    item['complexity']!,
                                  ),
                                );
                              }).toList(),
                            ),
                          ),
                        ),
                      ),
                      // Pagination at bottom right
                      if (totalPages > 1)
                        Container(
                          padding: const EdgeInsets.fromLTRB(10, 4, 10, 4),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              _buildPaginationControls(),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildValidationRuleItem(
    String title,
    String description,
    String category,
    String complexity,
  ) {
    return Builder(
      builder: (context) => InkWell(
        onTap: () {
          // Show AiValidationPopup when clicked
          showDialog(
            context: context,
            barrierDismissible: true,
            builder: (context) => const AiValidationPopup(),
          );
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              left: BorderSide(
                color: Colors.blue, // Blue vertical line
                width: 2,
              ),
              top: BorderSide(
                color: Colors.grey.shade300,
                width: 0.5,
              ),
              right: BorderSide(
                color: Colors.grey.shade300,
                width: 0.5,
              ),
              bottom: BorderSide(
                color: Colors.grey.shade300,
                width: 0.5,
              ),
            ),
            // borderRadius: BorderRadius.circular(6),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // First row - Title and Category
                        Padding(
                          padding: const EdgeInsets.only(top: 2),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                child: Tooltip(
                                  message: title,
                                  child: Text(
                                    title,
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.labelSmall(
                                          context),
                                      fontWeight: FontManager.bold,
                                      color: Colors.black,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      height: 1.2,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                category,
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.labelSmall(context),
                                  fontWeight: FontManager.regular,
                                  color: const Color(0xFF6B7280),
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  height: 1.2,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 6),
                        // Second row - Description
                        Tooltip(
                          message: description,
                          child: Text(
                            description,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.labelSmall(context),
                              fontWeight: FontManager.regular,
                              color: const Color(0xFF6B7280),
                              fontFamily: FontManager.fontFamilyTiemposText,
                              height: 1.2,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 6),
                  // Complexity badge on the right side
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getComplexityColor(complexity),
                    ),
                    child: Text(
                      complexity,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontManager.regular,
                        color: Colors.black,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        height: 1.2,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIndustryBundlesSection() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate available height for industry bundles
        // Since this section has natural height, we'll use a reasonable max height
        final maxBundleHeight =
            MediaQuery.of(context).size.height * 0.4; // 40% of screen height
        final dynamicBundleItemsPerPage =
            calculateBundleItemsPerPage(maxBundleHeight, context);

        // Update bundleItemsPerPage if it's different from current value
        if (bundleItemsPerPage != dynamicBundleItemsPerPage) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            setState(() {
              bundleItemsPerPage = dynamicBundleItemsPerPage;
              // Reset to first page if current page is now out of bounds
              if (currentBundlePage > totalBundlePages) {
                currentBundlePage = 1;
              }
            });
          });
        }

        return Container(
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFD0D0D0), width: 0.5),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Section 2 Header
              _buildSectionHeader(
                context: context,
                title: 'Industry Bundles',
                // No icon for Industry Bundles section
                gradientColors: const [Color(0xff835BED), Color(0xff7540E5)],
              ),
              // Section 2 Body (Natural Height)
              Container(
                color: Colors.white,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Column for bundle items without height constraints
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 10, 10, 0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children:
                            currentBundlePageItems.asMap().entries.map((entry) {
                          final index = entry.key;
                          final item = entry.value;
                          return Padding(
                            padding: EdgeInsets.only(
                              bottom: index < currentBundlePageItems.length - 1
                                  ? 4
                                  : 0,
                            ),
                            child: _buildIndustryBundleItem(
                              item['title']!,
                              item['match']!,
                              item['modules']!,
                              item['description']!,
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                    // Pagination at bottom right
                    if (totalBundlePages > 1)
                      Container(
                        padding: const EdgeInsets.fromLTRB(10, 4, 10, 4),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            _buildBundlePaginationControls(),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildIndustryBundleItem(
    String title,
    String match,
    String modules,
    String description,
  ) {
    return Builder(
      builder: (context) => Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            left: BorderSide(
              color: Color(0xff7846E7), // Blue vertical line
              width: 2,
            ),
            top: BorderSide(
              color: Colors.grey.shade300,
              width: 0.5,
            ),
            right: BorderSide(
              color: Colors.grey.shade300,
              width: 0.5,
            ),
            bottom: BorderSide(
              color: Colors.grey.shade300,
              width: 0.5,
            ),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Tooltip(
                  message: title,
                  mouseCursor: SystemMouseCursors.click,
                  child: Text(
                    title,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleSmall(context),
                      fontWeight: FontManager.semiBold,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      height: 1.2,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
                Text(
                  match,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.labelSmall(context),
                    fontWeight: FontManager.regular,
                    color: const Color(0xFF6B7280),
                    fontFamily: FontManager.fontFamilyTiemposText,
                    height: 1.2,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 3),
            Text(
              modules,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelSmall(context),
                fontWeight: FontManager.regular,
                color: const Color(0xFF6B7280),
                fontFamily: FontManager.fontFamilyTiemposText,
                height: 1.2,
              ),
            ),
            const SizedBox(height: 6),
            Tooltip(
              message: description,
              mouseCursor: SystemMouseCursors.click,
              child: Text(
                description,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontManager.regular,
                  color: const Color(0xFF6B7280),
                  fontFamily: FontManager.fontFamilyTiemposText,
                  height: 1.2,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: const Color(0xFF0058FF),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'APPLY ALL',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontManager.regular,
                      color: Colors.white,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      height: 1.2,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border:
                        Border.all(color: const Color(0xFFE5E5E5), width: 1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'VIEW',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontManager.regular,
                      color: const Color(0xFF6B7280),
                      fontFamily: FontManager.fontFamilyTiemposText,
                      height: 1.2,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getComplexityColor(String complexity) {
    switch (complexity.toUpperCase()) {
      case 'SIMPLE':
        return const Color(0xFFE8F5E8);
      case 'MODERATE':
        return const Color(0xFFFFF4E6);
      case 'COMPLEX':
        return const Color(0xFFFFE6E6);
      default:
        return const Color(0xFFF5F5F5);
    }
  }

  Widget _buildPaginationControls() {
    return Builder(
      builder: (context) => Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Previous button
          InkWell(
            onTap: currentPage > 1
                ? () {
                    setState(() {
                      currentPage--;
                    });
                  }
                : null,
            child: SizedBox(
              width: 24,
              height: 24,
              child: Icon(
                Icons.chevron_left,
                size: 24,
                color: Colors.grey.shade400,
              ),
            ),
          ),
          // Next button
          InkWell(
            onTap: currentPage < totalPages
                ? () {
                    setState(() {
                      currentPage++;
                    });
                  }
                : null,
            child: SizedBox(
              width: 24,
              height: 24,
              child: Icon(
                Icons.chevron_right,
                size: 24,
                color: Colors.grey.shade400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBundlePaginationControls() {
    return Builder(
      builder: (context) => Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Previous button
          InkWell(
            onTap: currentBundlePage > 1
                ? () {
                    setState(() {
                      currentBundlePage--;
                    });
                  }
                : null,
            child: SizedBox(
              width: 24,
              height: 24,
              child: Icon(
                Icons.chevron_left,
                size: 24,
                color: Colors.grey.shade400,
              ),
            ),
          ),
          // Next button
          InkWell(
            onTap: currentBundlePage < totalBundlePages
                ? () {
                    setState(() {
                      currentBundlePage++;
                    });
                  }
                : null,
            child: SizedBox(
              width: 24,
              height: 24,
              child: Icon(
                Icons.chevron_right,
                size: 24,
                color: Colors.grey.shade400,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
