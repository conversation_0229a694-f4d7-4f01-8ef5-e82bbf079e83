import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter/gestures.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';
import 'package:url_launcher/url_launcher.dart';
import '../utils/callback_interpreter.dart';

/// A configurable HTML content widget that renders HTML content with various styling options.
class HtmlWidget extends StatefulWidget {
  // Content properties
  final String htmlContent;

  // Appearance properties
  final Color textColor;
  final Color backgroundColor;
  final Color linkColor;
  final double fontSize;
  final FontWeight fontWeight;
  final String? fontFamily;
  final bool isDarkTheme;

  // Layout properties
  final double? width;
  final double height; // Fixed height with default value
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final double borderRadius;
  final bool hasBorder;
  final Color borderColor;
  final double borderWidth;
  final bool hasShadow;
  final double elevation;

  // Behavior properties
  final bool enableLinks;
  final bool scrollable;
  final ScrollPhysics? scrollPhysics;
  final double maxHeight;

  // Security properties
  final bool disableScripts;

  // Callbacks
  final Function(String)? onLinkTap;

  // Advanced interaction properties
  final void Function(bool)? onHover;
  final void Function(bool)? onFocus;
  final FocusNode? focusNode;
  final Color? hoverColor;
  final Color? focusColor;
  final bool enableFeedback;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;

  // HTML-specific properties
  final bool allowImages;
  final bool allowTables;
  final bool allowLists;
  final bool allowIframes;
  final bool allowForms;
  final List<String>? allowedTags;
  final List<String>? blockedTags;
  final Map<String, String>? customStyles;
  final bool sanitizeHtml;
  final bool renderMathContent;
  final bool renderCodeBlocks;
  final bool enableSyntaxHighlighting;
  final String? syntaxHighlightingTheme;

  // JSON configuration properties
  final Map<String, dynamic>? jsonCallbacks;
  final bool useJsonCallbacks;
  final Map<String, dynamic>? callbackState;
  final Map<String, Function>? customCallbackHandlers;
  final Map<String, dynamic>? jsonConfig;
  final bool useJsonValidation;
  final bool useJsonStyling;
  final bool useJsonFormatting;

  // HTML-specific JSON configuration
  final bool useJsonHtmlConfig;
  final Map<String, dynamic>? htmlConfig;

  const HtmlWidget({
    super.key,
    required this.htmlContent,
    this.textColor = const Color(0xFF333333),
    this.backgroundColor = Colors.white,
    this.linkColor = Colors.blue,
    this.fontSize = 14.0,
    this.fontWeight = FontManager.medium,
    this.fontFamily = FontManager.fontFamilyInter,
    this.isDarkTheme = false,
    this.width,
    //this.height = 200.0, // Default fixed height
    //this.padding = const EdgeInsets.all(8.0),
    //this.margin = const EdgeInsets.all(0),
    this.height = 0,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = const EdgeInsets.all(0),
    this.borderRadius = 4.0,
    this.hasBorder = false,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.enableLinks = true,
    this.scrollable = true,
    this.scrollPhysics,
    this.maxHeight = double.infinity,
    this.disableScripts = true,
    this.onLinkTap,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    // HTML-specific properties
    this.allowImages = true,
    this.allowTables = true,
    this.allowLists = true,
    this.allowIframes = false,
    this.allowForms = false,
    this.allowedTags,
    this.blockedTags,
    this.customStyles,
    this.sanitizeHtml = true,
    this.renderMathContent = false,
    this.renderCodeBlocks = true,
    this.enableSyntaxHighlighting = false,
    this.syntaxHighlightingTheme,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonValidation = false,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // HTML-specific JSON configuration
    this.useJsonHtmlConfig = false,
    this.htmlConfig,
  });

  /// Creates an HtmlWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the HtmlWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "htmlContent": "<h1>Hello World</h1><p>This is a paragraph.</p>",
  ///   "textColor": "#333333",
  ///   "backgroundColor": "#f5f5f5",
  ///   "enableLinks": true
  /// }
  /// ```
  factory HtmlWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle hex strings like "#FF0000"
        if (colorValue.startsWith('#')) {
          String hexColor = colorValue.substring(1);

          // Handle shorthand hex like #RGB
          if (hexColor.length == 3) {
            hexColor = hexColor.split('').map((c) => '$c$c').join('');
          }

          // Add alpha channel if missing
          if (hexColor.length == 6) {
            hexColor = 'FF$hexColor';
          }

          // Parse the hex value
          try {
            return Color(int.parse('0x$hexColor'));
          } catch (e) {
            // Silently handle the error and return null
            return null;
          }
        }

        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Colors.blue;
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'amber':
            return Colors.amber;
          case 'cyan':
            return Colors.cyan;
          case 'indigo':
            return Colors.indigo;
          case 'lime':
            return Colors.lime;
          case 'teal':
            return Colors.teal;
          default:
            return null;
        }
      } else if (colorValue is int) {
        // Handle integer color values
        return Color(colorValue);
      }

      return null;
    }

    // Parse text alignment
    TextAlign parseTextAlign(dynamic alignValue) {
      if (alignValue == null) return TextAlign.start;

      if (alignValue is String) {
        switch (alignValue.toLowerCase()) {
          case 'center':
            return TextAlign.center;
          case 'end':
          case 'right':
            return TextAlign.end;
          case 'start':
          case 'left':
            return TextAlign.start;
          case 'justify':
            return TextAlign.justify;
          default:
            return TextAlign.start;
        }
      }

      return TextAlign.start;
    }

    // Parse font weight
    FontWeight parseFontWeight(dynamic weightValue) {
      if (weightValue == null) return FontWeight.normal;

      if (weightValue is String) {
        switch (weightValue.toLowerCase()) {
          case 'bold':
            return FontWeight.bold;
          case 'normal':
            return FontWeight.normal;
          case 'light':
            return FontWeight.w300;
          default:
            return FontWeight.normal;
        }
      } else if (weightValue is int) {
        switch (weightValue) {
          case 100:
            return FontWeight.w100;
          case 200:
            return FontWeight.w200;
          case 300:
            return FontWeight.w300;
          case 400:
            return FontWeight.w400;
          case 500:
            return FontWeight.w500;
          case 600:
            return FontWeight.w600;
          case 700:
            return FontWeight.w700;
          case 800:
            return FontWeight.w800;
          case 900:
            return FontWeight.w900;
          default:
            return FontWeight.normal;
        }
      } else if (weightValue is bool && weightValue) {
        return FontWeight.bold;
      }

      return FontWeight.normal;
    }

    // Parse edge insets
    EdgeInsetsGeometry parseEdgeInsets(dynamic insetsValue) {
      if (insetsValue == null) {
        return const EdgeInsets.all(0.0);
      }

      if (insetsValue is Map<String, dynamic>) {
        final double left = (insetsValue['left'] as num?)?.toDouble() ?? 0.0;
        final double top = (insetsValue['top'] as num?)?.toDouble() ?? 0.0;
        final double right = (insetsValue['right'] as num?)?.toDouble() ?? 0.0;
        final double bottom =
            (insetsValue['bottom'] as num?)?.toDouble() ?? 0.0;

        if (insetsValue.containsKey('all')) {
          final double all = (insetsValue['all'] as num).toDouble();
          return EdgeInsets.all(all);
        } else if (insetsValue.containsKey('horizontal') ||
            insetsValue.containsKey('vertical')) {
          final double horizontal =
              (insetsValue['horizontal'] as num?)?.toDouble() ?? 0.0;
          final double vertical =
              (insetsValue['vertical'] as num?)?.toDouble() ?? 0.0;
          return EdgeInsets.symmetric(
            horizontal: horizontal,
            vertical: vertical,
          );
        } else {
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      } else if (insetsValue is num) {
        return EdgeInsets.all(insetsValue.toDouble());
      }

      return const EdgeInsets.all(8.0);
    }

    // Parse string list
    List<String>? parseStringList(dynamic listValue) {
      if (listValue == null) return null;

      if (listValue is List) {
        return List<String>.from(listValue.map((e) => e.toString()));
      } else if (listValue is String) {
        // Handle comma-separated string
        return listValue.split(',').map((e) => e.trim()).toList();
      }

      return null;
    }

    // Parse map of string to string
    Map<String, String>? parseStringMap(dynamic mapValue) {
      if (mapValue == null) return null;

      if (mapValue is Map) {
        return Map<String, String>.from(
          mapValue.map(
            (key, value) => MapEntry(key.toString(), value.toString()),
          ),
        );
      }

      return null;
    }

    // Parse JSON callback properties
    Map<String, dynamic>? jsonCallbacks;
    bool useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    if (json['callbacks'] != null) {
      if (json['callbacks'] is Map) {
        jsonCallbacks = Map<String, dynamic>.from(json['callbacks'] as Map);
        useJsonCallbacks = true;
      } else if (json['callbacks'] is String) {
        try {
          jsonCallbacks =
              jsonDecode(json['callbacks'] as String) as Map<String, dynamic>;
          useJsonCallbacks = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Parse additional callback properties for specific events
    if (json['onLinkTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onLinkTap'] = json['onLinkTap'];
      useJsonCallbacks = true;
    }

    if (json['onTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onTap'] = json['onTap'];
      useJsonCallbacks = true;
    }

    if (json['onDoubleTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onDoubleTap'] = json['onDoubleTap'];
      useJsonCallbacks = true;
    }

    if (json['onLongPress'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onLongPress'] = json['onLongPress'];
      useJsonCallbacks = true;
    }

    if (json['onHover'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onHover'] = json['onHover'];
      useJsonCallbacks = true;
    }

    if (json['onFocus'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onFocus'] = json['onFocus'];
      useJsonCallbacks = true;
    }

    // Parse HTML-specific configuration
    Map<String, dynamic>? htmlConfig;
    bool useJsonHtmlConfig = json['useJsonHtmlConfig'] as bool? ?? false;

    if (json['htmlConfig'] != null) {
      if (json['htmlConfig'] is Map) {
        htmlConfig = Map<String, dynamic>.from(json['htmlConfig'] as Map);
        useJsonHtmlConfig = true;
      } else if (json['htmlConfig'] is String) {
        try {
          htmlConfig =
              jsonDecode(json['htmlConfig'] as String) as Map<String, dynamic>;
          useJsonHtmlConfig = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Create the widget with all properties from JSON
    return HtmlWidget(
      htmlContent: json['htmlContent'] as String? ?? '',
      textColor: parseColor(json['textColor']) ?? const Color(0xFF333333),
      backgroundColor: parseColor(json['backgroundColor']) ?? Colors.white,
      linkColor: parseColor(json['linkColor']) ?? Colors.blue,
      fontSize:
          json['fontSize'] != null
              ? (json['fontSize'] as num).toDouble()
              : 14.0,
      fontWeight: parseFontWeight(json['fontWeight']),
      fontFamily: json['fontFamily'] as String?,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      width:
          json['width'] != null
              ? (json['width'].toString().toLowerCase() == 'infinity'
                  ? double.infinity
                  : (json['width'] as num).toDouble())
              : null,
      height:
          json['height'] != null
              ? (json['height'].toString().toLowerCase() == 'infinity'
                  ? double.infinity
                  : (json['height'] as num).toDouble())
              : 48.0, // Default fixed height
      padding: parseEdgeInsets(json['padding']),
      margin: parseEdgeInsets(json['margin']),
      borderRadius:
          json['borderRadius'] != null
              ? (json['borderRadius'] as num).toDouble()
              : 4.0,
      hasBorder: json['hasBorder'] as bool? ?? false,
      borderColor: parseColor(json['borderColor']) ?? Colors.grey,
      borderWidth:
          json['borderWidth'] != null
              ? (json['borderWidth'] as num).toDouble()
              : 1.0,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation:
          json['elevation'] != null
              ? (json['elevation'] as num).toDouble()
              : 2.0,
      enableLinks: json['enableLinks'] as bool? ?? true,
      scrollable: json['scrollable'] as bool? ?? true,
      scrollPhysics:
          json['scrollPhysics'] == 'bouncing'
              ? const BouncingScrollPhysics()
              : json['scrollPhysics'] == 'never'
              ? const NeverScrollableScrollPhysics()
              : const ClampingScrollPhysics(),
      maxHeight:
          json['maxHeight'] != null
              ? (json['maxHeight'].toString().toLowerCase() == 'infinity'
                  ? double.infinity
                  : (json['maxHeight'] as num).toDouble())
              : double.infinity,
      disableScripts: json['disableScripts'] as bool? ?? true,
      // Advanced interaction properties
      hoverColor: parseColor(json['hoverColor']),
      focusColor: parseColor(json['focusColor']),
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      // HTML-specific properties
      allowImages: json['allowImages'] as bool? ?? true,
      allowTables: json['allowTables'] as bool? ?? true,
      allowLists: json['allowLists'] as bool? ?? true,
      allowIframes: json['allowIframes'] as bool? ?? false,
      allowForms: json['allowForms'] as bool? ?? false,
      allowedTags: parseStringList(json['allowedTags']),
      blockedTags: parseStringList(json['blockedTags']),
      customStyles: parseStringMap(json['customStyles']),
      sanitizeHtml: json['sanitizeHtml'] as bool? ?? true,
      renderMathContent: json['renderMathContent'] as bool? ?? false,
      renderCodeBlocks: json['renderCodeBlocks'] as bool? ?? true,
      enableSyntaxHighlighting:
          json['enableSyntaxHighlighting'] as bool? ?? false,
      syntaxHighlightingTheme: json['syntaxHighlightingTheme'] as String?,
      // JSON configuration properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState:
          json['callbackState'] != null
              ? Map<String, dynamic>.from(json['callbackState'] as Map)
              : {},
      jsonConfig: json,
      useJsonValidation: json['useJsonValidation'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
      // HTML-specific JSON configuration
      useJsonHtmlConfig: useJsonHtmlConfig,
      htmlConfig: htmlConfig,
    );
  }

  /// Converts the widget configuration to a JSON map
  ///
  /// This method allows for serializing the widget's configuration to JSON,
  /// which can be useful for saving configurations or sending them to a server.
  Map<String, dynamic> toJson() {
    // Convert color to hex string
    String? colorToHex(Color? color) {
      if (color == null) return null;

      // Use a simple approach that works with all Flutter versions
      final hexString = color.toString();
      // Color(0xAARRGGBB) format -> extract RRGGBB part
      final hex = hexString.replaceAll('Color(0x', '').replaceAll(')', '');
      return '#${hex.substring(2)}'; // Skip alpha channel
    }

    // Convert edge insets to map
    Map<String, dynamic>? edgeInsetsToMap(EdgeInsetsGeometry insets) {
      if (insets is EdgeInsets) {
        if (insets.left == insets.top &&
            insets.left == insets.right &&
            insets.left == insets.bottom) {
          return {'all': insets.left};
        } else if (insets.left == insets.right && insets.top == insets.bottom) {
          return {'horizontal': insets.left, 'vertical': insets.top};
        } else {
          return {
            'left': insets.left,
            'top': insets.top,
            'right': insets.right,
            'bottom': insets.bottom,
          };
        }
      }
      return null;
    }

    // Create the JSON map
    final Map<String, dynamic> json = {
      'htmlContent': htmlContent,
      'textColor': colorToHex(textColor),
      'backgroundColor': colorToHex(backgroundColor),
      'linkColor': colorToHex(linkColor),
      'fontSize': fontSize,
      'fontWeight':
          fontWeight == FontWeight.bold
              ? 'bold'
              : fontWeight == FontWeight.w300
              ? 'light'
              : 'normal',
      'fontFamily': fontFamily,
      'isDarkTheme': isDarkTheme,
      'width': width == double.infinity ? 'infinity' : width,
      'height': height == double.infinity ? 'infinity' : height,
      'padding': edgeInsetsToMap(padding),
      'margin': edgeInsetsToMap(margin),
      'borderRadius': borderRadius,
      'hasBorder': hasBorder,
      'borderColor': colorToHex(borderColor),
      'borderWidth': borderWidth,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'enableLinks': enableLinks,
      'scrollable': scrollable,
      'scrollPhysics':
          scrollPhysics is BouncingScrollPhysics
              ? 'bouncing'
              : scrollPhysics is NeverScrollableScrollPhysics
              ? 'never'
              : 'clamping',
      'maxHeight': maxHeight == double.infinity ? 'infinity' : maxHeight,
      'disableScripts': disableScripts,
      'enableFeedback': enableFeedback,
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonValidation': useJsonValidation,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonHtmlConfig': useJsonHtmlConfig,

      // HTML-specific properties
      'allowImages': allowImages,
      'allowTables': allowTables,
      'allowLists': allowLists,
      'allowIframes': allowIframes,
      'allowForms': allowForms,
      'allowedTags': allowedTags,
      'blockedTags': blockedTags,
      'customStyles': customStyles,
      'sanitizeHtml': sanitizeHtml,
      'renderMathContent': renderMathContent,
      'renderCodeBlocks': renderCodeBlocks,
      'enableSyntaxHighlighting': enableSyntaxHighlighting,
      'syntaxHighlightingTheme': syntaxHighlightingTheme,
    };

    // Add hover and focus colors if they exist
    if (hoverColor != null) {
      json['hoverColor'] = colorToHex(hoverColor);
    }

    if (focusColor != null) {
      json['focusColor'] = colorToHex(focusColor);
    }

    // Add callbacks if they exist
    if (jsonCallbacks != null && jsonCallbacks!.isNotEmpty) {
      json['callbacks'] = jsonCallbacks;
    }

    // Add HTML config if it exists
    if (htmlConfig != null && htmlConfig!.isNotEmpty) {
      json['htmlConfig'] = htmlConfig;
    }

    return json;
  }

  @override
  State<HtmlWidget> createState() => _HtmlWidgetState();
}

class _HtmlWidgetState extends State<HtmlWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isHovered = false;
  bool _isFocused = false;

  // Map to store dynamic state for callbacks
  final Map<String, dynamic> _callbackState = {};

  // Map to store parsed configuration from JSON
  Map<String, dynamic>? _parsedJsonConfig;

  // Map to store HTML-specific configuration from JSON
  Map<String, dynamic>? _htmlConfig;

  // Focus node for handling focus events
  late FocusNode _focusNode;

  // Processed HTML content
  late String _processedHtml;

  @override
  void initState() {
    super.initState();

    // Initialize animation
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState.addAll(widget.callbackState!);
    }

    // Process HTML content
    _processedHtml = _processHtml();

    // Parse JSON configuration if provided
    if (widget.jsonConfig != null) {
      _parsedJsonConfig = Map<String, dynamic>.from(widget.jsonConfig!);

      // Apply initial JSON validation if enabled
      if (widget.useJsonValidation) {
        _applyJsonValidation();
      }

      // Apply initial JSON styling if enabled
      if (widget.useJsonStyling) {
        _applyJsonStyling();
      }

      // Apply initial JSON formatting if enabled
      if (widget.useJsonFormatting) {
        _applyJsonFormatting();
      }
    }

    // Parse HTML-specific configuration if provided
    if (widget.htmlConfig != null) {
      _htmlConfig = Map<String, dynamic>.from(widget.htmlConfig!);

      // Apply initial HTML configuration if enabled
      if (widget.useJsonHtmlConfig) {
        _applyHtmlConfig();
      }
    }

    // Execute onInit callback if defined in JSON
    _executeJsonCallback('onInit');
  }

  @override
  void dispose() {
    // Clean up focus node if we created it
    if (widget.focusNode == null) {
      _focusNode.removeListener(_handleFocusChange);
      _focusNode.dispose();
    }

    _animationController.dispose();
    super.dispose();
  }

  /// Handles focus changes
  void _handleFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;

      // Call the onFocus callback if provided
      if (widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }

      // Execute JSON callback if defined
      _executeJsonCallback('onFocus', _isFocused);
    });
  }

  /// Handles hover changes
  void _handleHoverChange(bool isHovered) {
    setState(() {
      _isHovered = isHovered;

      // Call the onHover callback if provided
      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }

      // Execute JSON callback if defined
      _executeJsonCallback('onHover', isHovered);
    });
  }

  /// Executes a callback defined in JSON
  ///
  /// This method interprets and executes a callback defined in the JSON configuration.
  /// It supports various callback types and provides access to the current state.
  void _executeJsonCallback(String callbackType, [dynamic data]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    // Check if the callback exists in the JSON configuration
    if (widget.jsonCallbacks!.containsKey(callbackType)) {
      final callback = widget.jsonCallbacks![callbackType];

      // Update the callback state with the current value
      _callbackState['htmlContent'] = widget.htmlContent;
      _callbackState['processedHtml'] = _processedHtml;

      // If data is provided, prepare it for the callback
      dynamic callbackValue;
      if (data != null) {
        callbackValue = data;
        _callbackState['data'] = data.toString();
      } else {
        callbackValue = widget.htmlContent;
      }

      // Execute the callback using the CallbackInterpreter
      try {
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: callbackValue,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      } catch (e) {
        debugPrint('Error executing JSON callback: $e');
      }
    }
  }

  /// Applies JSON validation rules to the HTML content
  ///
  /// This method applies validation rules defined in the JSON configuration.
  void _applyJsonValidation() {
    if (_parsedJsonConfig == null || !widget.useJsonValidation) return;

    // Example: Apply validation rules
    if (_parsedJsonConfig!.containsKey('validationRules')) {
      final rules = _parsedJsonConfig!['validationRules'];

      if (rules is Map<String, dynamic>) {
        // Apply validation rules to HTML content
        // Not fully implemented in this example
      }
    }
  }

  /// Applies JSON styling to the widget
  ///
  /// This method applies styling rules defined in the JSON configuration.
  void _applyJsonStyling() {
    if (_parsedJsonConfig == null || !widget.useJsonStyling) return;

    // This would be implemented to apply dynamic styling from JSON
    // Not fully implemented in this example
  }

  /// Applies JSON formatting to the current value
  ///
  /// This method applies formatting rules defined in the JSON configuration.
  void _applyJsonFormatting() {
    if (_parsedJsonConfig == null || !widget.useJsonFormatting) return;

    // This would be implemented to apply dynamic formatting from JSON
    // Not fully implemented in this example
  }

  /// Applies HTML-specific configuration to the widget
  ///
  /// This method applies HTML-specific rules defined in the JSON configuration.
  void _applyHtmlConfig() {
    if (_htmlConfig == null || !widget.useJsonHtmlConfig) return;

    // Example: Apply HTML-specific configuration
    if (_htmlConfig!.containsKey('allowedTags')) {
      // This would be implemented to configure allowed tags
      // Not fully implemented in this example
    }

    if (_htmlConfig!.containsKey('customStyles')) {
      // This would be implemented to configure custom styles
      // Not fully implemented in this example
    }

    if (_htmlConfig!.containsKey('sanitizationRules')) {
      // This would be implemented to configure sanitization rules
      // Not fully implemented in this example
    }
  }

  // Process HTML to remove scripts if needed
  String _processHtml() {
    String html = widget.htmlContent;

    if (widget.disableScripts) {
      // Simple regex to remove script tags and their content
      html = html.replaceAll(RegExp('<script.*?</script>', dotAll: true), '');

      // Remove inline JavaScript events (simplified approach)
      html = html.replaceAll(RegExp('on[a-zA-Z]+=(["|\'])(.*?)\\1'), '');
    }

    return html;
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else {
      return 14.0; // Default for very small screens
    }
  }

  EdgeInsets _getResponsivePadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return const EdgeInsets.symmetric(
        horizontal: 12.0,
        vertical: 4.0,
      ); // Extra Large
    } else if (screenWidth >= 1440) {
      return const EdgeInsets.symmetric(
        horizontal: 12.0,
        vertical: 8.0,
      ); // Large
    } else if (screenWidth >= 1280) {
      return const EdgeInsets.symmetric(horizontal: 8, vertical: 4.0); // Medium
    } else if (screenWidth >= 768) {
      return const EdgeInsets.symmetric(
        horizontal: 6.0,
        vertical: 4.0,
      ); // Small
    } else {
      return const EdgeInsets.symmetric(
        horizontal: 6.0,
        vertical: 1.0,
      ); // Default for very small screens
    }
  }

  double _getResponsiveHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    // Base height for input field
    double baseHeight;
    if (screenWidth > 1920) {
      baseHeight = 56.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      baseHeight = 48.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      baseHeight = 38.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      baseHeight = 32.0; // Small (768-1024px)
    } else {
      baseHeight = 32.0; // Default for very small screens
    }

    return baseHeight;
  }

  @override
  Widget build(BuildContext context) {
    // Process HTML content
    final processedHtml = _processHtml();

    // Create the styled container
    final styledWidget =
        widget.scrollable
            ? ConstrainedBox(
              constraints: BoxConstraints(maxHeight: widget.maxHeight),
              child: SingleChildScrollView(
                physics: widget.scrollPhysics ?? const ClampingScrollPhysics(),
                child: Html(
                  data: processedHtml,
                  style: {
                    "body": Style(
                      color:
                          widget.isDarkTheme ? Colors.white : Color(0xFF333333),
                      fontSize: FontSize(_getResponsiveFontSize(context)),
                      fontWeight: widget.fontWeight,
                      fontFamily: widget.fontFamily,
                      backgroundColor: Colors.transparent,
                      margin: Margins.zero,
                      padding: HtmlPaddings.zero,
                    ),

                    "p": Style(margin: Margins.only(bottom: 8)),
                    "h1, h2, h3, h4, h5, h6": Style(
                      fontWeight: FontWeight.bold,
                      margin: Margins.only(bottom: 8, top: 8),
                    ),
                    "ul, ol": Style(margin: Margins.only(left: 16, bottom: 8)),
                    "li": Style(margin: Margins.only(bottom: 4)),
                    "a": Style(
                      color: widget.linkColor,
                      textDecoration: TextDecoration.underline,
                    ),
                    "strong, b": Style(fontWeight: FontWeight.bold),
                    "em, i": Style(fontStyle: FontStyle.italic),
                    "code": Style(
                      backgroundColor: Colors.grey.shade200,
                      fontFamily: 'monospace',
                      padding: HtmlPaddings.symmetric(
                        horizontal: 4,
                        vertical: 2,
                      ),
                    ),
                    "pre": Style(
                      backgroundColor: Colors.grey.shade100,
                      padding: HtmlPaddings.all(8),
                      margin: Margins.only(bottom: 8),
                      fontFamily: 'monospace',
                    ),
                    "blockquote": Style(
                      border: Border(
                        left: BorderSide(color: Colors.grey, width: 4),
                      ),
                      padding: HtmlPaddings.only(left: 16),
                      margin: Margins.only(bottom: 8),
                      fontStyle: FontStyle.italic,
                    ),
                    // Table styles
                    "table": Style(
                      border: Border.all(color: Colors.grey.shade400, width: 1),
                      backgroundColor: Colors.white,
                      margin: Margins.only(bottom: 8, top: 8),
                      width: Width(100, Unit.percent),
                    ),
                    "thead": Style(backgroundColor: Colors.grey.shade100),
                    "th": Style(
                      padding: HtmlPaddings.all(8),
                      backgroundColor: Colors.grey.shade100,
                      fontWeight: FontWeight.bold,
                      border: Border.all(color: Colors.grey.shade400, width: 1),
                      textAlign: TextAlign.left,
                    ),
                    "td": Style(
                      padding: HtmlPaddings.all(8),
                      border: Border.all(color: Colors.grey.shade400, width: 1),
                      textAlign: TextAlign.left,
                    ),
                    "tr": Style(
                      border: Border.all(color: Colors.grey.shade400, width: 1),
                    ),
                    "tbody tr:nth-child(even)": Style(
                      backgroundColor: Colors.grey.shade50,
                    ),
                  },
                  onLinkTap:
                      widget.enableLinks
                          ? (url, attributes, element) {
                            if (widget.onLinkTap != null) {
                              widget.onLinkTap!(url ?? '');
                            } else if (url != null) {
                              launchUrl(Uri.parse(url));
                            }
                          }
                          : null,
                ),
              ),
            )
            : Padding(
              padding: _getResponsivePadding(context),
              child: Html(
                data: processedHtml,
                style: {
                  "body": Style(
                    color: widget.isDarkTheme ? Colors.white : widget.textColor,
                    fontSize: FontSize(widget.fontSize),
                    fontWeight: widget.fontWeight,
                    fontFamily: widget.fontFamily,
                    backgroundColor: Colors.transparent,
                    margin: Margins.zero,
                    padding: HtmlPaddings.zero,
                  ),
                  "p": Style(margin: Margins.only(bottom: 8)),
                  "h1, h2, h3, h4, h5, h6": Style(
                    fontWeight: FontWeight.bold,
                    margin: Margins.only(bottom: 8, top: 8),
                  ),
                  "ul, ol": Style(margin: Margins.only(left: 16, bottom: 8)),
                  "li": Style(margin: Margins.only(bottom: 4)),
                  "a": Style(
                    color: widget.linkColor,
                    textDecoration: TextDecoration.underline,
                  ),
                  "strong, b": Style(fontWeight: FontWeight.bold),
                  "em, i": Style(fontStyle: FontStyle.italic),
                  "code": Style(
                    backgroundColor: Colors.grey.shade200,
                    fontFamily: 'monospace',
                    padding: HtmlPaddings.symmetric(horizontal: 4, vertical: 2),
                  ),
                  "pre": Style(
                    backgroundColor: Colors.grey.shade100,
                    padding: HtmlPaddings.all(8),
                    margin: Margins.only(bottom: 8),
                    fontFamily: 'monospace',
                  ),
                  "blockquote": Style(
                    border: Border(
                      left: BorderSide(color: Colors.grey, width: 4),
                    ),
                    padding: HtmlPaddings.only(left: 16),
                    margin: Margins.only(bottom: 8),
                    fontStyle: FontStyle.italic,
                  ),
                  // Table styles
                  "table": Style(
                    border: Border.all(color: Colors.grey.shade400, width: 1),
                    backgroundColor: Colors.white,
                    margin: Margins.only(bottom: 8, top: 8),
                    width: Width(100, Unit.percent),
                  ),
                  "thead": Style(backgroundColor: Colors.grey.shade100),
                  "th": Style(
                    padding: HtmlPaddings.all(8),
                    backgroundColor: Colors.grey.shade100,
                    fontWeight: FontWeight.bold,
                    border: Border.all(color: Colors.grey.shade400, width: 1),
                    textAlign: TextAlign.left,
                  ),
                  "td": Style(
                    padding: HtmlPaddings.all(8),
                    border: Border.all(color: Colors.grey.shade400, width: 1),
                    textAlign: TextAlign.left,
                  ),
                  "tr": Style(
                    border: Border.all(color: Colors.grey.shade400, width: 1),
                  ),
                  "tbody tr:nth-child(even)": Style(
                    backgroundColor: Colors.grey.shade50,
                  ),
                },
                onLinkTap:
                    widget.enableLinks
                        ? (url, attributes, element) {
                          if (widget.onLinkTap != null) {
                            widget.onLinkTap!(url ?? '');
                          } else if (url != null) {
                            launchUrl(Uri.parse(url));
                          }
                        }
                        : null,
              ),
            );

    // Apply fade-in animation with focus and hover detection
    final fadeTransition = FadeTransition(
      opacity: _animation,
      child: styledWidget,
    );

    // Add hover detection
    final hoverWidget = MouseRegion(
      onEnter: (_) => _handleHoverChange(true),
      onExit: (_) => _handleHoverChange(false),
      child: fadeTransition,
    );

    // Add gesture detection for tap, double tap, and long press
    return GestureDetector(
      onTap: () {
        if (widget.onTap != null) {
          widget.onTap!();
        }
        _executeJsonCallback('onTap');

        // Request focus when tapped
        if (!_focusNode.hasFocus) {
          _focusNode.requestFocus();
        }
      },
      onDoubleTap:
          widget.onDoubleTap != null
              ? () {
                widget.onDoubleTap!();
                _executeJsonCallback('onDoubleTap');
              }
              : null,
      onLongPress:
          widget.onLongPress != null
              ? () {
                widget.onLongPress!();
                _executeJsonCallback('onLongPress');
              }
              : null,
      child: Focus(focusNode: _focusNode, child: hoverWidget),
    );
  }
}
