import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';

class CurrencyWidget extends StatefulWidget {
  // Configurable properties
  final double initialValue;
  final String currencySymbol;
  final String currencyCode;
  final String locale;
  final int decimalPlaces;
  final bool showSymbol;
  final bool symbolOnLeft;
  final bool allowNegative;
  final double minValue;
  final double maxValue;
  final Color textColor;
  final Color backgroundColor;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final bool hasBorder;
  final bool isReadOnly;
  final bool isDisabled;
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final bool showPrefix;
  final bool showSuffix;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final double fontSize;
  final FontWeight fontWeight;
  final bool isCompact;
  final bool useThousandsSeparator;
  final String thousandsSeparator;
  final String decimalSeparator;
  final Function(double)? onChanged;
  final Function(double)? onSubmitted;
  final TextAlign textAlign;
  final bool autofocus;
  final FocusNode? focusNode;
  final TextInputAction textInputAction;
  final bool enableInteractiveSelection;
  final bool obscureText;
  final bool autocorrect;
  final bool enableSuggestions;
  final int? maxLength;
  final bool hasShadow;
  final double elevation;
  final bool isDarkTheme;
  final bool useMaterial3;
  final bool hasAnimation;
  final String? prefixText;
  final String? suffixText;
  final double? testValue;
  final Color? hoverColor;
  final Color? focusColor;
  final EdgeInsets? insidePadding;
  final Color cursorColor;

  const CurrencyWidget({
    super.key,
    this.initialValue = 0.0,
    this.currencySymbol = '\$',
    this.currencyCode = 'USD',
    this.locale = 'en_US',
    this.decimalPlaces = 2,
    this.showSymbol = true,
    this.symbolOnLeft = true,
    this.allowNegative = true,
    this.minValue = double.negativeInfinity,
    this.maxValue = double.infinity,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = const Color(0xFFCCCCCC),
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.showPrefix = true,
    this.showSuffix = false,
    this.prefixIcon,
    this.suffixIcon,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isCompact = false,
    this.useThousandsSeparator = true,
    this.thousandsSeparator = ',',
    this.decimalSeparator = '.',
    this.onChanged,
    this.onSubmitted,
    this.textAlign = TextAlign.start,
    this.autofocus = false,
    this.focusNode,
    this.textInputAction = TextInputAction.done,
    this.enableInteractiveSelection = true,
    this.obscureText = false,
    this.autocorrect = false,
    this.enableSuggestions = false,
    this.maxLength,
    this.hasShadow = false,
    this.elevation = 0.0,
    this.isDarkTheme = false,
    this.useMaterial3 = false,
    this.hasAnimation = false,
    this.prefixText,
    this.suffixText,
    this.testValue,
    this.hoverColor,
    this.focusColor,
    this.insidePadding,
    this.cursorColor = Colors.grey,
  });

  /// Creates a CurrencyWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the CurrencyWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "initialValue": 100.0,
  ///   "currencySymbol": "$",
  ///   "currencyCode": "USD",
  ///   "decimalPlaces": 2,
  ///   "showSymbol": true,
  ///   "symbolOnLeft": true,
  ///   "label": "Amount",
  ///   "hasBorder": true
  /// }
  /// ```
  factory CurrencyWidget.fromJson(Map<String, dynamic> json) {
    // Handle text alignment
    TextAlign textAlign = TextAlign.start;
    if (json['textAlign'] != null) {
      switch (json['textAlign'].toString().toLowerCase()) {
        case 'center':
          textAlign = TextAlign.center;
          break;
        case 'end':
        case 'right':
          textAlign = TextAlign.end;
          break;
        case 'start':
        case 'left':
          textAlign = TextAlign.start;
          break;
      }
    }

    // Handle font weight
    FontWeight fontWeight = FontWeight.normal;
    if (json['fontWeight'] != null) {
      if (json['fontWeight'] == 'bold' || json['fontWeight'] == true) {
        fontWeight = FontWeight.bold;
      } else if (json['fontWeight'] == 'light') {
        fontWeight = FontWeight.w300;
      } else if (json['fontWeight'] is int) {
        final weight = json['fontWeight'] as int;
        switch (weight) {
          case 100:
            fontWeight = FontWeight.w100;
            break;
          case 200:
            fontWeight = FontWeight.w200;
            break;
          case 300:
            fontWeight = FontWeight.w300;
            break;
          case 400:
            fontWeight = FontWeight.w400;
            break;
          case 500:
            fontWeight = FontWeight.w500;
            break;
          case 600:
            fontWeight = FontWeight.w600;
            break;
          case 700:
            fontWeight = FontWeight.w700;
            break;
          case 800:
            fontWeight = FontWeight.w800;
            break;
          case 900:
            fontWeight = FontWeight.w900;
            break;
        }
      }
    }

    // Handle text input action
    TextInputAction textInputAction = TextInputAction.done;
    if (json['textInputAction'] != null) {
      switch (json['textInputAction'].toString().toLowerCase()) {
        case 'next':
          textInputAction = TextInputAction.next;
          break;
        case 'search':
          textInputAction = TextInputAction.search;
          break;
        case 'send':
          textInputAction = TextInputAction.send;
          break;
        case 'go':
          textInputAction = TextInputAction.go;
          break;
        case 'done':
        default:
          textInputAction = TextInputAction.done;
          break;
      }
    }

    return CurrencyWidget(
      initialValue: (json['initialValue'] as num?)?.toDouble() ?? 0.0,
      currencySymbol: json['currencySymbol'] as String? ?? '\$',
      currencyCode: json['currencyCode'] as String? ?? 'USD',
      locale: json['locale'] as String? ?? 'en_US',
      decimalPlaces: json['decimalPlaces'] as int? ?? 2,
      showSymbol: json['showSymbol'] as bool? ?? true,
      symbolOnLeft: json['symbolOnLeft'] as bool? ?? true,
      allowNegative: json['allowNegative'] as bool? ?? true,
      minValue:
          json['minValue'] != null
              ? (json['minValue'] as num).toDouble()
              : double.negativeInfinity,
      maxValue:
          json['maxValue'] != null
              ? (json['maxValue'] as num).toDouble()
              : double.infinity,
      textColor: _colorFromJson(json['textColor']) ?? Colors.black,
      backgroundColor: _colorFromJson(json['backgroundColor']) ?? Colors.white,
      borderColor:
          _colorFromJson(json['borderColor']) ?? const Color(0xFFCCCCCC),
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 4.0,
      hasBorder: json['hasBorder'] as bool? ?? true,
      isReadOnly: json['isReadOnly'] as bool? ?? false,
      isDisabled: json['isDisabled'] as bool? ?? false,
      label: json['label'] as String?,
      hint: json['hint'] as String?,
      helperText: json['helperText'] as String?,
      errorText: json['errorText'] as String?,
      hoverColor: _colorFromJson(json['hoverColor']),
      focusColor: _colorFromJson(json['focusColor']),
      showPrefix: json['showPrefix'] as bool? ?? true,
      showSuffix: json['showSuffix'] as bool? ?? false,
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 16.0,
      fontWeight: fontWeight,
      isCompact: json['isCompact'] as bool? ?? false,
      useThousandsSeparator: json['useThousandsSeparator'] as bool? ?? true,
      thousandsSeparator: json['thousandsSeparator'] as String? ?? ',',
      decimalSeparator: json['decimalSeparator'] as String? ?? '.',
      textAlign: textAlign,
      autofocus: json['autofocus'] as bool? ?? false,
      textInputAction: textInputAction,
      enableInteractiveSelection:
          json['enableInteractiveSelection'] as bool? ?? true,
      obscureText: json['obscureText'] as bool? ?? false,
      autocorrect: json['autocorrect'] as bool? ?? false,
      enableSuggestions: json['enableSuggestions'] as bool? ?? false,
      maxLength: json['maxLength'] as int?,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation: (json['elevation'] as num?)?.toDouble() ?? 0.0,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      useMaterial3: json['useMaterial3'] as bool? ?? false,
      hasAnimation: json['hasAnimation'] as bool? ?? false,
      prefixText: json['prefixText'] as String?,
      suffixText: json['suffixText'] as String?,
      cursorColor: _colorFromJson(json['cursorColor']) ?? Colors.black,
      insidePadding:
          json['insidePadding'] != null
              ? EdgeInsets.fromLTRB(
                (json['insidePadding']['left'] as num?)?.toDouble() ?? 12.0,
                (json['insidePadding']['top'] as num?)?.toDouble() ?? 0.0,
                (json['insidePadding']['right'] as num?)?.toDouble() ?? 12.0,
                (json['insidePadding']['bottom'] as num?)?.toDouble() ?? 0.0,
              )
              : null,
      onChanged: (value) {
        // This would be handled by the app in a real implementation
      },
      onSubmitted:
          json['hasSubmitHandler'] == true
              ? (value) {
                // This would be handled by the app in a real implementation
              }
              : null,
    );
  }

  /// Converts the CurrencyWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    String textAlignString = 'start';
    if (textAlign == TextAlign.center) {
      textAlignString = 'center';
    } else if (textAlign == TextAlign.end) {
      textAlignString = 'end';
    }

    String fontWeightString = 'normal';
    if (fontWeight == FontWeight.bold) {
      fontWeightString = 'bold';
    } else if (fontWeight == FontWeight.w300) {
      fontWeightString = 'light';
    }

    String textInputActionString = 'done';
    if (textInputAction == TextInputAction.next) {
      textInputActionString = 'next';
    } else if (textInputAction == TextInputAction.search) {
      textInputActionString = 'search';
    } else if (textInputAction == TextInputAction.send) {
      textInputActionString = 'send';
    } else if (textInputAction == TextInputAction.go) {
      textInputActionString = 'go';
    }

    return {
      'initialValue': initialValue,
      'currencySymbol': currencySymbol,
      'currencyCode': currencyCode,
      'locale': locale,
      'decimalPlaces': decimalPlaces,
      'showSymbol': showSymbol,
      'symbolOnLeft': symbolOnLeft,
      'allowNegative': allowNegative,
      if (minValue != double.negativeInfinity) 'minValue': minValue,
      if (maxValue != double.infinity) 'maxValue': maxValue,
      'textColor': _colorToJson(textColor),
      'backgroundColor': _colorToJson(backgroundColor),
      'borderColor': _colorToJson(borderColor),
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'hasBorder': hasBorder,
      'isReadOnly': isReadOnly,
      'isDisabled': isDisabled,
      'hoverColor': _colorToJson(hoverColor!),
      'focusColor': _colorToJson(focusColor!),
      if (label != null) 'label': label,
      if (hint != null) 'hint': hint,
      if (helperText != null) 'helperText': helperText,
      if (errorText != null) 'errorText': errorText,
      'showPrefix': showPrefix,
      'showSuffix': showSuffix,
      'fontSize': fontSize,
      'fontWeight': fontWeightString,
      'isCompact': isCompact,
      'useThousandsSeparator': useThousandsSeparator,
      'thousandsSeparator': thousandsSeparator,
      'decimalSeparator': decimalSeparator,
      'textAlign': textAlignString,
      'autofocus': autofocus,
      'textInputAction': textInputActionString,
      'enableInteractiveSelection': enableInteractiveSelection,
      'obscureText': obscureText,
      'autocorrect': autocorrect,
      'enableSuggestions': enableSuggestions,
      if (maxLength != null) 'maxLength': maxLength,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'isDarkTheme': isDarkTheme,
      'useMaterial3': useMaterial3,
      'hasAnimation': hasAnimation,
      if (prefixText != null) 'prefixText': prefixText,
      if (suffixText != null) 'suffixText': suffixText,
      'cursorColor': _colorToJson(cursorColor),
      if (insidePadding != null)
        'insidePadding': {
          'left': insidePadding!.left,
          'top': insidePadding!.top,
          'right': insidePadding!.right,
          'bottom': insidePadding!.bottom,
        },
      'hasSubmitHandler': onSubmitted != null,
    };
  }

  /// Converts a JSON color value to a Flutter Color
  ///
  /// Accepts hex strings (e.g., "#FF0000"), color names (e.g., "red"),
  /// or integer values (e.g., 0xFFFF0000)
  static Color? _colorFromJson(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      // Handle hex strings like "#FF0000"
      if (colorValue.startsWith('#')) {
        String hexColor = colorValue.substring(1);

        // Handle shorthand hex like #RGB
        if (hexColor.length == 3) {
          hexColor = hexColor.split('').map((c) => '$c$c').join('');
        }

        // Add alpha channel if missing
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }

        // Parse the hex value
        try {
          return Color(int.parse('0x$hexColor'));
        } catch (e) {
          // Silently handle the error and return null
          return null;
        }
      }

      // Handle named colors
      switch (colorValue.toLowerCase()) {
        case 'red':
          return Colors.red;
        case 'blue':
          return Colors.blue;
        case 'green':
          return Colors.green;
        case 'yellow':
          return Colors.yellow;
        case 'orange':
          return Colors.orange;
        case 'purple':
          return Colors.purple;
        case 'pink':
          return Colors.pink;
        case 'brown':
          return Colors.brown;
        case 'grey':
        case 'gray':
          return Colors.grey;
        case 'black':
          return Colors.black;
        case 'white':
          return Colors.white;
        case 'amber':
          return Colors.amber;
        case 'cyan':
          return Colors.cyan;
        case 'indigo':
          return Colors.indigo;
        case 'lime':
          return Colors.lime;
        case 'teal':
          return Colors.teal;
        default:
          return null;
      }
    } else if (colorValue is int) {
      // Handle integer color values
      return Color(colorValue);
    }

    return null;
  }

  /// Converts a Flutter Color to a JSON representation
  ///
  /// Returns a hex string (e.g., "#FF0000") or a color name for standard colors
  static String _colorToJson(Color color) {
    // Handle standard colors by name for better readability
    if (color == Colors.red) return 'red';
    if (color == Colors.blue) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.amber) return 'amber';
    if (color == Colors.cyan) return 'cyan';
    if (color == Colors.indigo) return 'indigo';
    if (color == Colors.lime) return 'lime';
    if (color == Colors.teal) return 'teal';

    // For MaterialColor, preserve the original color name if possible
    if (color is MaterialColor) {
      if (color == Colors.red) return 'red';
      if (color == Colors.blue) return 'blue';
      if (color == Colors.green) return 'green';
      if (color == Colors.yellow) return 'yellow';
      if (color == Colors.orange) return 'orange';
      if (color == Colors.purple) return 'purple';
      if (color == Colors.pink) return 'pink';
      if (color == Colors.brown) return 'brown';
      if (color == Colors.grey) return 'grey';
      if (color == Colors.amber) return 'amber';
      if (color == Colors.cyan) return 'cyan';
      if (color == Colors.indigo) return 'indigo';
      if (color == Colors.lime) return 'lime';
      if (color == Colors.teal) return 'teal';

      // If it's a MaterialColor but not one of the standard ones,
      // fall back to the hex representation of the primary value
      color = color.shade500;
    }

    // Convert to RGB format and create a hex string for other colors
    final r = color.toString().substring(10, 12);
    final g = color.toString().substring(12, 14);
    final b = color.toString().substring(14, 16);

    return '#$r$g$b';
  }

  @override
  State<CurrencyWidget> createState() => _CurrencyWidgetState();
}

class _CurrencyWidgetState extends State<CurrencyWidget>
    with SingleTickerProviderStateMixin {
  late TextEditingController _controller;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late FocusNode _focusNode;
  double _currentValue = 0.0;
  bool _hasFocus = false;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    // Use test value if provided (for testing purposes)
    if (widget.testValue != null) {
      _currentValue = widget.testValue!;
    } else {
      _currentValue = widget.initialValue;
    }
    _controller = TextEditingController(text: _formatCurrency(_currentValue));
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    _animationController.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _hasFocus = _focusNode.hasFocus;
      if (_hasFocus && widget.hasAnimation) {
        _animationController.forward();
      } else if (widget.hasAnimation) {
        _animationController.reverse();
      }
    });
  }

  String _formatCurrency(double value) {
    // Format the value based on the widget's configuration
    final NumberFormat formatter = NumberFormat.currency(
      locale: widget.locale,
      symbol: widget.showSymbol ? widget.currencySymbol : '',
      decimalDigits: widget.decimalPlaces,
    );

    // Apply the basic formatting
    String formatted = formatter.format(value);

    // Apply custom formatting manually
    if (!widget.useThousandsSeparator) {
      // Remove thousands separator
      formatted = formatted.replaceAll(',', '');
    } else if (widget.thousandsSeparator != ',') {
      // Replace default thousands separator with custom one
      formatted = formatted.replaceAll(',', widget.thousandsSeparator);
    }

    // Handle custom decimal separator if different from default
    if (widget.decimalSeparator != '.') {
      formatted = formatted.replaceAll('.', widget.decimalSeparator);
    }

    // Remove the symbol if not showing it
    if (!widget.showSymbol) {
      formatted = formatted.replaceAll(widget.currencySymbol, '').trim();
    }

    // Handle symbol position
    if (widget.showSymbol && !widget.symbolOnLeft) {
      formatted = formatted.replaceAll('${widget.currencySymbol} ', '');
      formatted = formatted.replaceAll(widget.currencySymbol, '');
      formatted = '$formatted ${widget.currencySymbol}';
    }

    return formatted;
  }

  double _parseFormattedValue(String formattedValue) {
    // Remove currency symbol, thousands separators, and handle decimal separator
    String cleanValue = formattedValue;

    if (widget.showSymbol) {
      cleanValue = cleanValue.replaceAll(widget.currencySymbol, '');
    }

    cleanValue = cleanValue.replaceAll(widget.thousandsSeparator, '');
    cleanValue = cleanValue.replaceAll(widget.decimalSeparator, '.');
    cleanValue = cleanValue.trim();

    // Handle empty or invalid input
    if (cleanValue.isEmpty) {
      return 0.0;
    }

    try {
      return double.parse(cleanValue);
    } catch (e) {
      return _currentValue; // Return the previous value if parsing fails
    }
  }

  void _handleValueChanged(String text) {
    final double parsedValue = _parseFormattedValue(text);

    // Apply min/max constraints
    double constrainedValue = parsedValue;
    if (parsedValue < widget.minValue) {
      constrainedValue = widget.minValue;
    } else if (parsedValue > widget.maxValue) {
      constrainedValue = widget.maxValue;
    }

    // Don't allow negative values if not permitted
    if (!widget.allowNegative && constrainedValue < 0) {
      constrainedValue = 0;
    }

    setState(() {
      _currentValue = constrainedValue;
    });

    if (widget.onChanged != null) {
      widget.onChanged!(_currentValue);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Create the input formatter for currency
    final List<TextInputFormatter> inputFormatters = [
      FilteringTextInputFormatter.allow(
        RegExp(
          r'[0-9${widget.decimalSeparator}${widget.thousandsSeparator}\-\+]',
        ),
      ),
    ];

    if (!widget.allowNegative) {
      inputFormatters.add(FilteringTextInputFormatter.deny(RegExp(r'[\-]')));
    }

    if (widget.maxLength != null) {
      inputFormatters.add(LengthLimitingTextInputFormatter(widget.maxLength!));
    }

    // Create the text field with responsive container wrapper (same as decimal widget)
    Widget textField = MouseRegion(
      onEnter: (_) {
        setState(() {
          _isHovered = true;
        });
        // Call the onHover callback if provided
        // if (widget.onHover != null) {
        //   widget.onHover!(true);
        // }
      },
      onExit: (_) {
        setState(() {
          _isHovered = false;
        });
        // Call the onHover callback if provided
        // if (widget.onHover != null) {
        //   widget.onHover!(false);
        // }
      },
      child: Theme(
        data: Theme.of(context).copyWith(
          inputDecorationTheme: const InputDecorationTheme(
            hoverColor: Colors.transparent,
            focusColor: Colors.transparent,
          ),
        ),
        child: Container(
          // decoration: BoxDecoration(
          //   borderRadius: BorderRadius.circular(0),
          // ),
          height: _getResponsiveHeight(context),
          child: TextField(
            controller: _controller,
            focusNode: _focusNode,
            keyboardType: const TextInputType.numberWithOptions(
              decimal: true,
              signed: true,
            ),
            textAlign: widget.textAlign,
            cursorColor: widget.cursorColor,

            style: FontManager.getCustomStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontWeight: FontManager.medium,
              color:
                  widget.isDisabled
                      ? Colors.grey
                      : widget.textColor.withOpacity(0.6),
              fontSize: _getResponsiveValueFontSize(context),
            ),
            decoration: InputDecoration(
              hintText: widget.hint,
              helperText: widget.helperText,
              errorText: widget.errorText,
              filled: true,
              fillColor:
                  widget.isDisabled
                      ? Colors.grey.shade200
                      : widget.backgroundColor,
              contentPadding: _getResponsivePadding(context),

              // prefixIcon:
              //     widget.showPrefix
              //         ? Padding(
              //           padding: const EdgeInsets.only(left: 0.0, right: 0),
              //           child: SvgPicture.asset(
              //             _isHovered
              //                 ? 'assets/images/doller-hover.svg'
              //                 : 'assets/images/doller.svg',
              //             package: 'ui_controls_library',

              //             fit: BoxFit.scaleDown,
              //           ),
              //         )
              //         : null,
              suffixIcon:
                  widget.showSuffix && widget.suffixIcon != null
                      ? Icon(widget.suffixIcon)
                      : null,
              prefixText: widget.prefixText,

              // prefixStyle: TextStyle(
              //   //fontSize: _getResponsiveIconSize(context),
              //   fontSize: 16,
              //   fontFamily: 'Inter',

              //   //fontWeight: FontWeight.w600,
              //   color:
              //       _isHovered
              //           ? (widget.hoverColor ?? const Color(0xFF0058FF))
              //           : _hasFocus
              //           ? (widget.focusColor ?? const Color(0xFF0058FF))
              //           : widget.borderColor,
              // ),
              prefixStyle: FontManager.getCustomStyle(
                fontSize: _getResponsiveValueFontSize(context),
                fontFamily: FontManager.fontFamilyInter,
                //fontWeight: FontWeight.w600,
                color:
                    _isHovered
                        ? (widget.hoverColor ?? const Color(0xFF0058FF))
                        : _hasFocus
                        ? (widget.focusColor ?? const Color(0xFF0058FF))
                        : widget.borderColor,
              ),
              suffixText: widget.suffixText,
              border:
                  widget.hasBorder
                      ? OutlineInputBorder(
                        // borderRadius: BorderRadius.circular(widget.borderRadius),
                        borderSide: BorderSide(
                          color: widget.borderColor,
                          width: widget.borderWidth,
                        ),
                      )
                      : InputBorder.none,
              enabledBorder:
                  widget.hasBorder
                      ? OutlineInputBorder(
                        borderRadius: BorderRadius.circular(4),
                        borderSide: BorderSide(
                          color:
                              _isHovered
                                  ? (widget.hoverColor ??
                                      const Color(
                                        0xFF0058FF,
                                      )) // Use hoverColor or default pink
                                  : widget.borderColor,
                          width: _isHovered ? 1.0 : widget.borderWidth,
                        ),
                      )
                      : null,
              focusedBorder:
                  widget.hasBorder
                      ? OutlineInputBorder(
                        borderRadius: BorderRadius.circular(4),
                        borderSide: BorderSide(
                          color:
                              widget.focusColor ??
                              const Color(
                                0xFF0058FF,
                              ), // Use focusColor or default pink
                          width: 1.0,
                        ),
                      )
                      : null,
              disabledBorder:
                  widget.hasBorder
                      ? OutlineInputBorder(
                        borderRadius: BorderRadius.circular(4),
                        borderSide: BorderSide(
                          color: Colors.grey.shade400,
                          width: widget.borderWidth,
                        ),
                      )
                      : null,
            ),
            inputFormatters: inputFormatters,
            enabled: !widget.isDisabled && !widget.isReadOnly,
            readOnly: widget.isReadOnly,
            autofocus: widget.autofocus,
            textInputAction: widget.textInputAction,
            enableInteractiveSelection: widget.enableInteractiveSelection,
            obscureText: widget.obscureText,
            autocorrect: widget.autocorrect,
            enableSuggestions: widget.enableSuggestions,
            onChanged: (value) {
              _handleValueChanged(value);
            },
            onSubmitted: (value) {
              if (widget.onSubmitted != null) {
                widget.onSubmitted!(_currentValue);
              }
            },
            onTap: () {
              if (widget.isCompact) {
                // Select all text when tapped in compact mode
                _controller.selection = TextSelection(
                  baseOffset: 0,
                  extentOffset: _controller.text.length,
                );
              }
            },
          ),
        ),
      ),
    );

    // Apply animation if enabled
    if (widget.hasAnimation) {
      textField = AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(scale: _scaleAnimation.value, child: child);
        },
        child: textField,
      );
    }

    // Apply shadow if enabled
    if (widget.hasShadow) {
      return Card(
        elevation: widget.elevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius),
        ),
        color: Colors.transparent,
        shadowColor: Colors.black.withAlpha(76), // 0.3 opacity = 76/255 alpha
        child: textField,
      );
    }

    return textField;
  }

  double _getResponsiveValueFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else {
      return 14.0; // Default for very small screens
    }
  }
}

double _getResponsiveHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 56.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 48.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 32.0; // Small (768-1024px)
  } else {
    return 32.0; // Default for very small screens
  }
}

double _getResponsiveIconSize(BuildContext context) {
  final double screenWidth = MediaQuery.of(context).size.width;
  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else if (screenWidth >= 768) {
    return 12.0; // Small
  } else {
    return 10.0; // Extra Small (fallback for very small screens)
  }
}

EdgeInsets _getResponsivePadding(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth >= 1440) {
    return const EdgeInsets.symmetric(
      horizontal: 16.0,
      vertical: 4.0,
    ); // Extra Large
  } else if (screenWidth >= 1280) {
    return const EdgeInsets.symmetric(
      horizontal: 12.0,
      vertical: 3.0,
    ); // Large// Large
  } else if (screenWidth >= 768) {
    return const EdgeInsets.symmetric(
      horizontal: 8.0,
      vertical: 2.0,
    ); // Medium// Medium
  } else {
    return const EdgeInsets.symmetric(
      horizontal: 8.0,
      vertical: 1.0,
    ); // Default for very small screens
  }
}
